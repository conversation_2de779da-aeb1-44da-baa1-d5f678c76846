// SyncLogs-related TypeScript interfaces and types

/**
 * Sync summary containing detailed sync statistics
 */
export interface SyncSummary {
  errors: number;
  apiCalls: number;
  duration: string;
  warnings: number;
  totalMonths: number;
  summaryRecords: number;
  processedMonths: number;
  trackingRecords: number;
}

/**
 * Main sync log interface matching the API response structure
 */
export interface SyncLog {
  Id: string;
  RequestId: string;
  CompanyId: string;
  ApiName: string;
  Method: string;
  ApiUrl: string;
  IntegrationName: string;
  StatusCode: string;
  Duration: string;
  Message: string;
  Entity: string;
  TriggeredBy: string;
  SyncStatus: 'SUCCESS' | 'ERROR' | 'WARNING' | 'PENDING' | 'IN_PROGRESS';
  ApiRequest: any | null;
  ApiResponse: any | null;
  ErrorDetails: string | null;
  SyncSummary: SyncSummary | null;
  StartedAt: string;
  CompletedAt: string;
  CreatedAt: string;
  UpdatedAt: string;
}

export interface SyncLogFilters {
  dateFrom?: string;
  dateTo?: string;
  startDate?: string;
  endDate?: string;
  endpoint?: string;
  status?: string;
  search?: string;
  searchQuery?: string;
  dateRange?: {
    start?: string;
    end?: string;
  };
}

export interface SyncLogsState {
  logs: SyncLog[];
  filteredLogs: SyncLog[];
  selectedLog: SyncLog | null;
  filters: SyncLogFilters;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FetchSyncLogsResponse {
  logs: SyncLog[];
}

export interface RetrySyncOperationParams {
  logId: string;
  endpoint: string;
}

export interface RetrySyncOperationResponse {
  success: boolean;
  newLog?: SyncLog;
  error?: string;
}
