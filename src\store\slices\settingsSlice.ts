import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState, SyncEntity, XeroModule, ApiError } from '../types';
import { fetchXeroModules } from '../actions/settings.actions';

/**
 * Transform XeroModule to SyncEntity for frontend display
 */
const transformXeroModuleToSyncEntity = (module: XeroModule): SyncEntity => {
  return {
    id: module.id,
    name: module.moduleName,
    lastSync: module.lastSyncTime || 'Never',
    status: module.lastSyncTime ? 'success' : 'pending',
    recordCount: 0, // Will be updated after sync
    enabled: true, // All modules are enabled by default
    syncFrequency: 'manual',
    companyId: module.companyId,
    createdAt: module.createdAt,
    updatedAt: module.updatedAt,
  };
};

// Initial state
const initialState: SettingsState = {
  entities: [],
  xeroModules: [],
  syncSchedule: {
    enabled: true,
    frequency: 'daily',
    time: '18:00',
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const syncEntity = createAsyncThunk(
  'settings/syncEntity',
  async (entityName: string, { rejectWithValue }) => {
    try {
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 2000));


      const currentTime = new Date().toLocaleString('en-CA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      return {
        entityName,
        lastSync: currentTime,
        status: 'success' as const,
        recordCount: Math.floor(Math.random() * 100) + 10 // Random count for demo
      };
    } catch (error) {
      return rejectWithValue({
        message: `Failed to sync ${entityName}`,
        code: 'SYNC_ENTITY_FAILED'
      } as ApiError);
    }
  }
);

export const syncAllEntities = createAsyncThunk(
  'settings/syncAllEntities',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { settings: SettingsState };
      const enabledEntities = state.settings.entities.filter(entity => entity.enabled);

      // Simulate syncing all entities
      await new Promise(resolve => setTimeout(resolve, 3000));


      const currentTime = new Date().toLocaleString('en-CA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      return {
        syncTime: currentTime,
        entityCount: enabledEntities.length
      };
    } catch (error) {
      return rejectWithValue({
        message: 'Failed to sync all entities',
        code: 'SYNC_ALL_FAILED'
      } as ApiError);
    }
  }
);

export const updateSyncSchedule = createAsyncThunk(
  'settings/updateSyncSchedule',
  async (schedule: SettingsState['syncSchedule'], { rejectWithValue }) => {
    try {
      // Simulate API call to update schedule
      await new Promise(resolve => setTimeout(resolve, 500));


      return schedule;
    } catch (error) {
      return rejectWithValue({
        message: 'Failed to update sync schedule',
        code: 'UPDATE_SCHEDULE_FAILED'
      } as ApiError);
    }
  }
);

export const fetchSyncSettings = createAsyncThunk(
  'settings/fetchSyncSettings',
  async (_, { rejectWithValue }) => {
    try {
      const { settingsApi } = await import('../../api/settings.api');

      const response = await settingsApi.fetchSyncSettings();
      return {
        entities: response.entities,
        syncSchedule: {
          enabled: response.globalSettings.autoSync,
          frequency: 'daily' as const,
          time: '18:00',
        }
      };
    } catch (error) {
      return rejectWithValue({
        message: error instanceof Error ? error.message : 'Failed to fetch sync settings',
        code: 'FETCH_SETTINGS_FAILED'
      } as ApiError);
    }
  }
);

// Slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    toggleEntityEnabled: (state, action: PayloadAction<string>) => {
      const entity = state.entities.find(e => e.name === action.payload);
      if (entity) {
        entity.enabled = !entity.enabled;
      }
    },
    updateEntityStatus: (state, action: PayloadAction<{ name: string; status: SyncEntity['status'] }>) => {
      const entity = state.entities.find(e => e.name === action.payload.name);
      if (entity) {
        entity.status = action.payload.status;
      }
    },
    setSyncSchedule: (state, action: PayloadAction<SettingsState['syncSchedule']>) => {
      state.syncSchedule = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetSettings: (state) => {
      state.entities = [];
      state.xeroModules = [];
      state.syncSchedule = {
        enabled: true,
        frequency: 'daily',
        time: '18:00',
      };
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Sync single entity
    builder
      .addCase(syncEntity.pending, (state, action) => {
        const entity = state.entities.find(e => e.name === action.meta.arg);
        if (entity) {
          entity.status = 'syncing';
        }
        state.error = null;
      })
      .addCase(syncEntity.fulfilled, (state, action) => {
        const entity = state.entities.find(e => e.name === action.payload.entityName);
        if (entity) {
          entity.status = action.payload.status;
          entity.lastSync = action.payload.lastSync;
          entity.recordCount = action.payload.recordCount;
        }
      })
      .addCase(syncEntity.rejected, (state, action: any) => {
        const entityName = action.meta.arg;
        const entity = state.entities.find(e => e.name === entityName);
        if (entity) {
          entity.status = 'pending';
        }
        state.error = action.payload?.message || `Failed to sync ${entityName}`;
      });

    // Sync all entities
    builder
      .addCase(syncAllEntities.pending, (state) => {
        state.isLoading = true;
        state.entities.forEach(entity => {
          if (entity.enabled) {
            entity.status = 'syncing';
          }
        });
        state.error = null;
      })
      .addCase(syncAllEntities.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entities.forEach(entity => {
          if (entity.enabled) {
            entity.status = 'success';
            entity.lastSync = action.payload.syncTime;
          }
        });
      })
      .addCase(syncAllEntities.rejected, (state, action: any) => {
        state.isLoading = false;
        state.entities.forEach(entity => {
          if (entity.status === 'syncing') {
            entity.status = 'pending';
          }
        });
        state.error = action.payload?.message || 'Failed to sync all entities';
      });

    // Update sync schedule
    builder
      .addCase(updateSyncSchedule.fulfilled, (state, action) => {
        state.syncSchedule = action.payload;
      })
      .addCase(updateSyncSchedule.rejected, (state, action: any) => {
        state.error = action.payload?.message || 'Failed to update sync schedule';
      });

    // Fetch Xero modules
    builder
      .addCase(fetchXeroModules.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchXeroModules.fulfilled, (state, action) => {
        state.isLoading = false;
        state.xeroModules = action.payload;
        // Transform Xero modules to sync entities
        state.entities = action.payload.map(transformXeroModuleToSyncEntity);
      })
      .addCase(fetchXeroModules.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to fetch Xero modules';
      });

    // Fetch sync settings
    builder
      .addCase(fetchSyncSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSyncSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entities = action.payload.entities;
        state.syncSchedule = action.payload.syncSchedule;
      })
      .addCase(fetchSyncSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || 'Failed to fetch sync settings';
      });
  },
});

export const {
  toggleEntityEnabled,
  updateEntityStatus,
  setSyncSchedule,
  clearError,
  resetSettings
} = settingsSlice.actions;

export default settingsSlice.reducer;
