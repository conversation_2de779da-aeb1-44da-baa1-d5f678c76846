// Auth Types
export interface CompanyInfo {
  name: string;
  shortCode: string;
  baseCurrency: string;
  countryCode: string;
  taxNumber: string;
  organisationType: string;
}

export interface AuthState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  companyInfo: CompanyInfo | null;
  accessToken: string | null;
  refreshToken: string | null;
}

// Companies Types
export interface Organization {
  id: string;
  name: string;
  shortCode: string;
  tenantId?: string;
  connectionId?: string;
  lastSync?: string;
}

export interface CompaniesState {
  organizations: Organization[];
  selectedOrganization: string | null;
  isLoading: boolean;
  error: string | null;
}

// Sync Logs Types - moved to src/store/types/syncLogs.types.ts
// Import from there instead of using these types

// Settings Types

/**
 * Xero Module from backend API
 */
export interface XeroModule {
  id: string;
  companyId: string;
  moduleName: string;
  lastSyncTime: string | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Sync Entity for frontend display (transformed from XeroModule)
 */
export interface SyncEntity {
  id: string;
  name: string;
  lastSync: string;
  status: 'success' | 'pending' | 'syncing' | 'error';
  recordCount?: number;
  enabled: boolean;
  syncFrequency: 'manual' | 'hourly' | 'daily' | 'weekly';
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface SettingsState {
  entities: SyncEntity[];
  xeroModules: XeroModule[];
  syncSchedule: {
    enabled: boolean;
    frequency: 'hourly' | 'daily' | 'weekly';
    time: string;
  };
  isLoading: boolean;
  error: string | null;
}

// API Response Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
