import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Settings,
  User,
  Link,
  Building2,
  Zap,
  LogOut,
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { logoutUser } from "@/store/actions/auth.actions";
import { toast } from "@/components/ui/sonner";

export function AppSidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user, isLoading }: any = useAppSelector((state) => state.auth);
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
  } = useAppSelector((state) => state.companies);

  const handleLogout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();

      // Show success toast
      toast.success("Signed out successfully", {
        description: "You have been logged out of your account.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Logout failed:", error);
      // Show error toast
      toast.error("Logout failed", {
        description:
          typeof error === "string"
            ? error
            : "There was an issue signing you out. Please try again.",
        duration: 4000,
      });
    }
  };

  // Logic to determine if sidebar menu items should be disabled
  const shouldDisableMenuItems = () => {
    // If no companies are available, disable menu items
    if (!organizations || organizations.length === 0) {
      return true;
    }

    // If a company is selected, check if it's disconnected
    if (selectedOrganization) {
      const selectedCompany = organizations.find(
        (org) => org.id === selectedOrganization
      );
      if (selectedCompany) {
        const orgData = selectedCompany as any;
        const status = orgData.organizationStatus || orgData.ConnectionStatus;
        if (status === "DISCONNECTED" || status === "SUSPENDED") {
          return true;
        }
      }
    }

    // If companies are available and selected company is connected, enable menu items
    return false;
  };

  const isMenuDisabled = shouldDisableMenuItems();

  // Get current company connection status
  const getConnectionStatus = () => {
    // Show loading state if companies are being fetched
    if (companiesLoading) {
      return {
        status: "loading",
        label: "Loading...",
        icon: AlertCircle,
        color: "text-blue-500",
      };
    }

    if (!organizations || organizations.length === 0) {
      return {
        status: "no-companies",
        label: "No Companies",
        icon: XCircle,
        color: "text-red-500",
      };
    }

    if (selectedOrganization) {
      const selectedCompany = organizations.find(
        (org) => org.id === selectedOrganization
      );
      if (selectedCompany) {
        const orgData = selectedCompany as any;
        const status = orgData.organizationStatus || orgData.ConnectionStatus;

        switch (status) {
          case "ACTIVE":
          case "CONNECTED":
            return {
              status: "connected",
              label: "Connected",
              icon: CheckCircle,
              color: "text-green-500",
            };
          case "DISCONNECTED":
            return {
              status: "disconnected",
              label: "Disconnected",
              icon: XCircle,
              color: "text-red-500",
            };
          case "SUSPENDED":
            return {
              status: "suspended",
              label: "Suspended",
              icon: AlertCircle,
              color: "text-yellow-500",
            };
          case "PENDING":
            return {
              status: "pending",
              label: "Pending",
              icon: AlertCircle,
              color: "text-blue-500",
            };
          default:
            return {
              status: "unknown",
              label: "Unknown",
              icon: AlertCircle,
              color: "text-gray-500",
            };
        }
      }
    }

    return {
      status: "no-selection",
      label: "No Selection",
      icon: AlertCircle,
      color: "text-gray-500",
    };
  };

  const connectionStatus = getConnectionStatus();

  // Get current active route
  const getCurrentRoute = () => {
    const path = location.pathname;
    if (path === "/dashboard" || path === "/") return "integration";
    if (path === "/companies") return "companies";
    if (path === "/sync-logs") return "logs";
    if (path === "/settings") return "settings";
    return "integration";
  };

  const activeRoute = getCurrentRoute();

  // Handle navigation
  const handleNavigation = (route: string) => {
    if (isMenuDisabled && route !== "integration") {
      toast.warning("Connection Required", {
        description:
          "Please connect to a company first to access this feature.",
        duration: 3000,
      });
      return;
    }

    switch (route) {
      case "integration":
        navigate("/dashboard");
        break;
      case "companies":
        navigate("/companies");
        break;
      case "logs":
        navigate("/sync-logs");
        break;
      case "settings":
        navigate("/settings");
        break;
      default:
        navigate("/dashboard");
    }
  };

  const menuItems = [
    {
      id: "integration",
      title: "Integrations",
      icon: Link,
      description: "OAuth flow & software selection",
      disabled: false, // Integration is always available
      route: "/dashboard",
    },
    {
      id: "companies",
      title: "Connected Companies",
      icon: Building2,
      description: "View connected organizations",
      disabled: isMenuDisabled,
      route: "/companies",
    },
    {
      id: "logs",
      title: "Sync Logs",
      icon: FileText,
      description: "View synchronization history",
      disabled: isMenuDisabled,
      route: "/sync-logs",
    },
    {
      id: "settings",
      title: "Sync Settings",
      icon: Settings,
      description: "Configure sync preferences",
      disabled: isMenuDisabled,
      route: "/settings",
    },
  ];

  return (
    <Sidebar
      variant="sidebar"
      collapsible="icon"
      className="border-r-0 shadow-lg"
    >
      <SidebarHeader className="border-b border-stone-200 bg-stone-50 p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500 rounded-xl">
                <Zap className="h-7 w-7 text-white" />
              </div>
              <div className="text-stone-800 group-data-[collapsible=icon]:hidden">
                <h2 className="font-bold text-xl">Assiduous</h2>
                <p className="text-stone-600 text-sm opacity-90">
                  Integration Hub
                </p>
              </div>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4 py-6 bg-stone-50">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-stone-500 uppercase tracking-wider mb-4 group-data-[collapsible=icon]:hidden">
            Main Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    onClick={() => handleNavigation(item.id)}
                    isActive={activeRoute === item.id}
                    disabled={item.disabled}
                    className={`
                      w-full h-14 px-3 text-left justify-start rounded-lg transition-all duration-200
                      ${
                        activeRoute === item.id
                          ? "bg-gradient-to-r from-stone-700 to-stone-800 text-stone-100 shadow-md hover:from-stone-800 hover:to-stone-900"
                          : "hover:bg-stone-100 text-stone-700 hover:text-stone-900"
                      }
                      ${
                        item.disabled
                          ? "opacity-50 cursor-not-allowed"
                          : "cursor-pointer"
                      }
                    `}
                    size="lg"
                    tooltip={item.title}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div
                        className={`p-2.5 rounded-lg transition-colors flex-shrink-0 ${
                          activeRoute === item.id
                            ? "bg-white/20"
                            : "bg-stone-200 group-hover:bg-stone-300"
                        }`}
                      >
                        <item.icon
                          className={`h-4 w-4 ${
                            activeRoute === item.id
                              ? "text-stone-100"
                              : "text-stone-600"
                          }`}
                        />
                      </div>
                      <div className="flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                        <div
                          className={`text-sm font-medium leading-none mb-1 ${
                            activeRoute === item.id
                              ? "text-stone-100"
                              : "text-stone-700"
                          }`}
                        >
                          {item.title}
                        </div>
                        <div
                          className={`text-xs leading-tight truncate ${
                            activeRoute === item.id
                              ? "text-stone-200"
                              : "text-stone-500"
                          }`}
                        >
                          {item.description}
                        </div>
                      </div>
                    </div>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-stone-200 bg-stone-100 p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm">
            <div className="p-2.5 bg-stone-200 rounded-lg flex-shrink-0">
              {isLoading && !user ? (
                <Loader2 className="h-4 w-4 text-stone-600 animate-spin" />
              ) : (
                <User className="h-4 w-4 text-stone-600" />
              )}
            </div>
            <div className="min-w-0 group-data-[collapsible=icon]:hidden">
              <div className="font-medium text-stone-900 leading-none mb-1">
                {isLoading && !user ? (
                  <div className="h-4 bg-stone-300 rounded animate-pulse w-16"></div>
                ) : (
                  user?.Name || "User"
                )}
              </div>
              <div className="text-xs text-stone-600">
                {isLoading && !user ? (
                  <div className="h-3 bg-stone-300 rounded animate-pulse w-24 mt-1"></div>
                ) : (
                  user?.Email || "Loading..."
                )}
              </div>
            </div>
          </div>
          <Button
            onClick={handleLogout}
            disabled={isLoading}
            variant="outline"
            size="sm"
            className="w-full group-data-[collapsible=icon]:hidden"
          >
            <LogOut className="h-4 w-4 mr-2" />
            {isLoading ? "Signing out..." : "Sign Out"}
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
