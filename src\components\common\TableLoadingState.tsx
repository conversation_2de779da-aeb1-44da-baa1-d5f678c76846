import React from 'react';
import { Loader2, Database } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface TableLoadingStateProps {
  rows?: number;
  columns?: number;
  showIcon?: boolean;
  message?: string;
  className?: string;
}

/**
 * TableLoadingState Component
 * 
 * Provides a skeleton loading state for tables while data is being fetched.
 * Shows animated placeholders that match the expected table structure.
 */
export const TableLoadingState: React.FC<TableLoadingStateProps> = ({
  rows = 5,
  columns = 4,
  showIcon = true,
  message = 'Loading data...',
  className,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Loading message with icon */}
      {showIcon && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-3 text-muted-foreground">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="text-sm font-medium">{message}</span>
          </div>
        </div>
      )}

      {/* Table skeleton */}
      <div className="space-y-3">
        {/* Header row */}
        <div className="flex gap-4 pb-2 border-b">
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton
              key={`header-${index}`}
              className={cn(
                'h-4',
                index === 0 ? 'flex-1' : 'w-24'
              )}
            />
          ))}
        </div>

        {/* Data rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={`row-${rowIndex}`} className="flex gap-4 py-2">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton
                key={`cell-${rowIndex}-${colIndex}`}
                className={cn(
                  'h-4',
                  colIndex === 0 ? 'flex-1' : 'w-24'
                )}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Compact loading state for smaller tables or cards
 */
export const CompactTableLoadingState: React.FC<{
  rows?: number;
  message?: string;
}> = ({
  rows = 3,
  message = 'Loading...',
}) => {
  return (
    <div className="py-8 space-y-4">
      <div className="flex items-center justify-center">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm">{message}</span>
        </div>
      </div>
      
      <div className="space-y-2">
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="flex gap-3">
            <Skeleton className="h-4 flex-1" />
            <Skeleton className="h-4 w-16" />
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Card-based loading state for grid layouts
 */
export const CardLoadingState: React.FC<{
  cards?: number;
  className?: string;
}> = ({
  cards = 6,
  className,
}) => {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4', className)}>
      {Array.from({ length: cards }).map((_, index) => (
        <div key={index} className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-4 flex-1" />
          </div>
          <Skeleton className="h-3 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
          <div className="flex gap-2 pt-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * List loading state for simple lists
 */
export const ListLoadingState: React.FC<{
  items?: number;
  showAvatar?: boolean;
  className?: string;
}> = ({
  items = 5,
  showAvatar = false,
  className,
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center gap-3 py-2">
          {showAvatar && <Skeleton className="h-8 w-8 rounded-full" />}
          <div className="flex-1 space-y-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      ))}
    </div>
  );
};

export default TableLoadingState;
