// Integration Page - Xero OAuth flow & software selection
// Handles Xero connection, disconnection, and integration management

import React, { useCallback } from "react";
import { toast } from "@/components/ui/sonner";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import XeroConnection from "@/components/XeroConnection";
import { useOrganizations } from "@/hooks/useOrganizations";
import { OrganizationsLoadingOverlay } from "@/components/common/PageLoadingOverlay";
import { PageHeader } from "@/components/common/PageHeader";

/**
 * Integration Page Component
 *
 * Main integration interface providing:
 * - Xero OAuth2 connection flow
 * - Organization selection and management
 * - Connection status monitoring
 * - Integration management tools
 *
 * @returns {JSX.Element} The complete integration interface
 */
const Integration: React.FC = () => {
  // Use custom hook for organizations management
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
    connectedOrganizations,
    refreshOrganizations,
    selectOrganization,
  } = useOrganizations({
    autoLoad: true,
    autoSelect: true,
    showErrorToasts: true,
    showSuccessToasts: true,
  });

  /**
   * Handle organization selection change
   */
  const handleOrganizationChange = useCallback(
    (organizationId: string) => {
      selectOrganization(organizationId);
    },
    [selectOrganization]
  );

  /**
   * Handle successful connection
   */
  const handleConnect = useCallback(() => {
    // Refresh organizations after successful connection
    refreshOrganizations();
    toast.success("Successfully connected!", {
      description: "Your integration is now active.",
      duration: 3000,
    });
  }, [refreshOrganizations]);

  /**
   * Handle disconnection
   */
  const handleDisconnect = useCallback(() => {
    // Refresh organizations after disconnection
    refreshOrganizations();
    toast.info("Disconnected", {
      description: "Your integration has been disconnected.",
      duration: 3000,
    });
  }, [refreshOrganizations]);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-amber-50 relative">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0 relative">
          {/* Header */}
          <PageHeader
            icon="🔗"
            title="Integrations"
            description="Connect and manage your software integrations"
            organizations={organizations}
            selectedOrganization={selectedOrganization}
            connectedOrganizations={connectedOrganizations}
            isLoading={companiesLoading}
            showConnectionStatus={true}
            onOrganizationChange={handleOrganizationChange}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-8 bg-amber-50 relative">
            <div className="max-w-7xl mx-auto">
              <XeroConnection
                onConnect={handleConnect}
                onDisconnect={handleDisconnect}
              />
            </div>

            {/* Loading Overlay */}
            <OrganizationsLoadingOverlay isLoading={companiesLoading} />
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default Integration;
