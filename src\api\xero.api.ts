// Xero Integration API Service
// Handles all Xero OAuth2 and integration-related API calls

import { apiClient, handleApiError } from './axios.config';

/**
 * Xero OAuth2 authorization URL response
 */
export interface XeroAuthUrlResponse {
  authUrl: string;
  state: string;
}

/**
 * Xero OAuth2 callback response
 */
export interface XeroCallbackResponse {
  success: boolean;
  message: string;
  tenantId?: string;
  tenantName?: string;
  expiresAt?: string;
}

/**
 * Xero connection status response
 */
export interface XeroConnectionStatus {
  isConnected: boolean;
  tenantId?: string;
  tenantName?: string;
  expiresAt?: string;
  scopes?: string[];
}

/**
 * Xero token refresh response
 */
export interface XeroTokenRefreshResponse {
  success: boolean;
  message: string;
  expiresAt?: string;
}

/**
 * Xero API service for OAuth2 and integration management
 */
export const xeroApi = {
  /**
   * Generate Xero OAuth2 authorization URL
   * GET /api/v1/xero/connect
   */
  getAuthUrl: async (): Promise<XeroAuthUrlResponse> => {
    try {
      const response = await apiClient.get<XeroAuthUrlResponse>('/xero/connect');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Handle Xero OAuth2 callback
   * GET /api/v1/xero/callback
   */
  handleCallback: async (params: {
    code: string;
    state: string;
    scope?: string;
    authKey?: string;
  }): Promise<XeroCallbackResponse> => {
    try {
      const response = await apiClient.get<XeroCallbackResponse>('/xero/callback', {
        params,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Disconnect Xero connection
   * POST /api/v1/xero/disconnect
   */
  disconnect: async (): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await apiClient.post<{ success: boolean; message: string }>('/xero/disconnect');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Manually refresh Xero tokens
   * POST /api/v1/xero/refresh
   */
  refreshTokens: async (): Promise<XeroTokenRefreshResponse> => {
    try {
      const response = await apiClient.post<XeroTokenRefreshResponse>('/xero/refresh');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get current Xero connection status
   * Helper method to check connection status
   */
  getConnectionStatus: async (): Promise<XeroConnectionStatus> => {
    try {
      const response = await apiClient.get<XeroConnectionStatus>('/xero/status');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },
};

export default xeroApi;
