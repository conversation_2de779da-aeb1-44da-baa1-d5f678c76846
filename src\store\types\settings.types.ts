// Settings-related TypeScript interfaces and types

/**
 * Xero Module from backend API
 */
export interface XeroModule {
  id: string;
  companyId: string;
  moduleName: string;
  lastSyncTime: string | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Sync Entity for frontend display (transformed from XeroModule)
 */
export interface SyncEntity {
  id: string;
  name: string;
  lastSync: string;
  status: 'success' | 'pending' | 'syncing' | 'error';
  recordCount?: number;
  enabled: boolean;
  syncFrequency: 'manual' | 'hourly' | 'daily' | 'weekly';
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface SettingsState {
  entities: SyncEntity[];
  xeroModules: XeroModule[];
  isLoading: boolean;
  error: string | null;
  lastSyncAll: string | null;
}

export interface FetchSyncSettingsResponse {
  entities: SyncEntity[];
  lastSyncAll: string | null;
}

export interface SyncEntityResponse {
  success: boolean;
  entity?: SyncEntity;
  error?: string;
}

export interface SyncAllEntitiesResponse {
  success: boolean;
  entities?: SyncEntity[];
  lastSyncAll?: string;
  error?: string;
}
