// Auth API service functions

import { apiClient, handleApiError } from './axios.config';
import { LoginCredentials, RegisterCredentials, AuthResponse, User } from '../store/types/auth.types';

// Auth API endpoints
export const authApi = {
  // User Authentication

  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post('/user/login', credentials);


      // Store token in localStorage
      if (response.data?.data?.token) {
        localStorage.setItem('auth_token', response.data?.data?.token);
      }

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Register user
  register: async (credentials: RegisterCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post('/user/register', credentials);

      // Don't store token after registration - user should login manually
      // This ensures proper authentication flow and better security

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Logout user
  logout: async (): Promise<{ success: boolean; message: string }> => {
    const clearTokens = () => {
      // Clear all authentication tokens from localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('xero_access_token');
      localStorage.removeItem('xero_refresh_token');
    };

    try {
      // Send logout request to server to invalidate token
      const response = await apiClient.post('/user/logout');

      // Clear tokens after successful server logout
      clearTokens();

      return {
        success: true,
        message: 'Successfully logged out'
      };
    } catch (error) {
      // Even if API call fails, clear local storage for security
      clearTokens();


      // Don't throw error - logout should always succeed locally
      return {
        success: true,
        message: 'Logged out locally (server logout failed)'
      };
    }
  },



  // Get current user info
  getCurrentUser: async (): Promise<User> => {
    try {
      const response = await apiClient.get('/user/me');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};

export default authApi;
