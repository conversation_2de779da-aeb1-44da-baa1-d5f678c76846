// Sync Logs Page - Synchronization history and monitoring
// Displays sync logs, status, and detailed sync information

import React, { useCallback } from "react";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import SyncLogs from "@/components/SyncLogs";
import { useOrganizations } from "@/hooks/useOrganizations";
import { SyncLoadingOverlay } from "@/components/common/PageLoadingOverlay";
import { PageHeader } from "@/components/common/PageHeader";

/**
 * Sync Logs Page Component
 *
 * Synchronization monitoring interface providing:
 * - Detailed sync history and logs
 * - Real-time sync status monitoring
 * - Error tracking and troubleshooting
 * - Sync performance analytics
 *
 * @returns {JSX.Element} The complete sync logs interface
 */
const SyncLogsPage: React.FC = () => {
  // Use custom hook for organizations management
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
    connectedOrganizations,
    refreshOrganizations,
    selectOrganization,
  } = useOrganizations({
    autoLoad: true,
    autoSelect: true,
    showErrorToasts: true,
    showSuccessToasts: true,
  });

  /**
   * Handle organization selection change
   */
  const handleOrganizationChange = useCallback(
    (organizationId: string) => {
      selectOrganization(organizationId);
    },
    [selectOrganization]
  );

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-amber-50 relative">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0 relative">
          {/* Header */}
          <PageHeader
            icon="📋"
            title="Sync Logs"
            description="Monitor synchronization history and troubleshoot issues"
            organizations={organizations}
            selectedOrganization={selectedOrganization}
            connectedOrganizations={connectedOrganizations}
            isLoading={companiesLoading}
            showConnectionStatus={true}
            onOrganizationChange={handleOrganizationChange}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-8 bg-amber-50 relative">
            <div className="max-w-7xl mx-auto">
              <SyncLogs />
            </div>

            {/* Loading Overlay */}
            <SyncLoadingOverlay isLoading={companiesLoading} operation="logs" />
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default React.memo(SyncLogsPage);
