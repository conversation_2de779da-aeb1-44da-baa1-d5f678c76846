import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building, RefreshCw } from "lucide-react";
import type { Organization } from "@/store/types/companies.types";

export interface PageHeaderProps {
  /** Page icon (emoji or React component) */
  icon: string | React.ReactNode;
  /** Page title */
  title: string;
  /** Page description */
  description: string;
  /** List of organizations for the dropdown */
  organizations?: Organization[];
  /** Currently selected organization ID */
  selectedOrganization?: string | null;
  /** Connected organizations only */
  connectedOrganizations?: Organization[];
  /** Loading state for organizations */
  isLoading?: boolean;
  /** Show connection status indicator */
  showConnectionStatus?: boolean;
  /** Custom actions to display in the header */
  actions?: React.ReactNode;
  /** Organization change handler */
  onOrganizationChange?: (organizationId: string) => void;
  /** Refresh handler */
  onRefresh?: () => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Common Page Header Component
 *
 * Provides a consistent header layout across all pages with:
 * - Sidebar trigger
 * - Page title and description
 * - Organization selector (when organizations are available)
 * - Refresh button
 * - Connection status indicator
 * - Custom actions support
 *
 * @example
 * ```tsx
 * <PageHeader
 *   icon="🔗"
 *   title="Integrations"
 *   description="Connect and manage your software integrations"
 *   organizations={organizations}
 *   selectedOrganization={selectedOrganization}
 *   connectedOrganizations={connectedOrganizations}
 *   isLoading={companiesLoading}
 *   showConnectionStatus={true}
 *   onOrganizationChange={handleOrganizationChange}
 *   onRefresh={handleRefresh}
 * />
 * ```
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  icon,
  title,
  description,
  organizations = [],
  selectedOrganization,
  connectedOrganizations = [],
  isLoading = false,
  showConnectionStatus = true,
  actions,
  onOrganizationChange,
  onRefresh,
  className = "",
}) => {
  return (
    <header
      className={`flex h-20 shrink-0 items-center gap-4 border-b border-amber-200 bg-white/95 backdrop-blur-sm px-8 shadow-sm ${className}`}
    >
      <SidebarTrigger className="-ml-1 hover:bg-amber-50 transition-colors" />

      <div className="flex-1">
        <div className="flex items-center gap-3 mb-1">
          {typeof icon === "string" ? (
            <span className="text-2xl">{icon}</span>
          ) : (
            icon
          )}
          <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
        </div>
        <p className="text-sm text-gray-600">{description}</p>
      </div>

      {/* Organization Selector and Actions */}
      {organizations.length > 0 && (
        <div className="flex items-center gap-4">
          <Select
            value={selectedOrganization || ""}
            onValueChange={onOrganizationChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-64 bg-white border-amber-200 hover:border-amber-300 focus:border-amber-400">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-amber-600" />
                <SelectValue placeholder="Select organization..." />
              </div>
            </SelectTrigger>
            <SelectContent>
              {connectedOrganizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  <div className="flex flex-col gap-1 py-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{org.name}</span>
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {onRefresh && (
            <Button
              onClick={onRefresh}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="border-amber-200 hover:border-amber-300 hover:bg-amber-50"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          )}

          {showConnectionStatus && connectedOrganizations.length > 0 && (
            <div className="flex items-center gap-2 px-3 py-1.5 bg-emerald-50 border border-emerald-200 rounded-full">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-emerald-700">
                Connected
              </span>
            </div>
          )}

          {/* Custom Actions */}
          {actions}
        </div>
      )}
    </header>
  );
};

export default PageHeader;
