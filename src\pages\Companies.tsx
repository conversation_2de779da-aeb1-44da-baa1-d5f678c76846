// Companies Page - Connected organizations management
// Displays and manages connected companies and their details

import React, { useCallback } from "react";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import CompaniesListing from "@/components/CompaniesListing";
import { useNavigate } from "react-router-dom";
import { useOrganizations } from "@/hooks/useOrganizations";
import { OrganizationsLoadingOverlay } from "@/components/common/PageLoadingOverlay";
import { PageHeader } from "@/components/common/PageHeader";

/**
 * Companies Page Component
 *
 * Connected companies management interface providing:
 * - List of all connected organizations
 * - Company details and status information
 * - Organization selection and management
 * - Company-specific actions and settings
 *
 * @returns {JSX.Element} The complete companies management interface
 */
const CompaniesPage: React.FC = () => {
  const navigate = useNavigate();

  // Use custom hook for organizations management
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
    connectedOrganizations,
    refreshOrganizations,
    selectOrganization,
  } = useOrganizations({
    autoLoad: true,
    autoSelect: true,
    showErrorToasts: true,
    showSuccessToasts: true,
  });

  /**
   * Handle organization selection change
   */
  const handleOrganizationChange = useCallback(
    (organizationId: string) => {
      selectOrganization(organizationId);
    },
    [selectOrganization]
  );

  /**
   * Handle back to integration navigation
   */
  const handleBackToIntegration = useCallback(() => {
    navigate("/dashboard");
  }, [navigate]);

  /**
   * Handle manual refresh
   */
  const handleRefresh = useCallback(async () => {
    try {
      await refreshOrganizations();
    } catch (error) {
      // Error handling is done in the hook
    }
  }, [refreshOrganizations]);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-amber-50 relative">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0 relative">
          {/* Header */}
          <PageHeader
            icon="🏢"
            title="Connected Companies"
            description="Manage your connected organizations and their settings"
            organizations={organizations}
            selectedOrganization={selectedOrganization}
            connectedOrganizations={connectedOrganizations}
            isLoading={companiesLoading}
            showConnectionStatus={true}
            onOrganizationChange={handleOrganizationChange}
            onRefresh={handleRefresh}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-8 bg-amber-50 relative">
            <div className="max-w-7xl mx-auto">
              <CompaniesListing onBack={handleBackToIntegration} />
            </div>

            {/* Loading Overlay */}
            <OrganizationsLoadingOverlay isLoading={companiesLoading} />
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default React.memo(CompaniesPage);
