import React from 'react';
import { LucideIcon, Database, Search, RefreshCw, Plus, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface TableEmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
    icon?: LucideIcon;
    disabled?: boolean;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
    icon?: LucideIcon;
    disabled?: boolean;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * TableEmptyState Component
 * 
 * A specialized empty state component designed for tables and data grids.
 * Provides consistent styling and behavior for "no data" scenarios.
 */
export const TableEmptyState: React.FC<TableEmptyStateProps> = ({
  icon: Icon = Database,
  title,
  description,
  action,
  secondaryAction,
  className,
  size = 'md',
}) => {
  const sizeClasses = {
    sm: {
      container: 'py-8',
      icon: 'h-8 w-8',
      title: 'text-lg',
      description: 'text-sm',
      spacing: 'space-y-3',
    },
    md: {
      container: 'py-12',
      icon: 'h-12 w-12',
      title: 'text-xl',
      description: 'text-base',
      spacing: 'space-y-4',
    },
    lg: {
      container: 'py-16',
      icon: 'h-16 w-16',
      title: 'text-2xl',
      description: 'text-lg',
      spacing: 'space-y-6',
    },
  };

  const classes = sizeClasses[size];

  return (
    <div className={cn(
      'flex flex-col items-center justify-center text-center',
      classes.container,
      className
    )}>
      <div className={cn('flex flex-col items-center', classes.spacing)}>
        {/* Icon */}
        <div className="flex items-center justify-center rounded-full bg-muted/50 p-4">
          <Icon className={cn(classes.icon, 'text-muted-foreground')} />
        </div>

        {/* Content */}
        <div className="space-y-2 max-w-md">
          <h3 className={cn('font-semibold text-foreground', classes.title)}>
            {title}
          </h3>
          <p className={cn('text-muted-foreground leading-relaxed', classes.description)}>
            {description}
          </p>
        </div>

        {/* Actions */}
        {(action || secondaryAction) && (
          <div className="flex flex-col sm:flex-row gap-3 mt-2">
            {action && (
              <Button
                onClick={action.onClick}
                variant={action.variant || 'default'}
                disabled={action.disabled}
                className="flex items-center gap-2"
              >
                {action.icon && <action.icon className="h-4 w-4" />}
                {action.label}
              </Button>
            )}
            {secondaryAction && (
              <Button
                onClick={secondaryAction.onClick}
                variant={secondaryAction.variant || 'outline'}
                disabled={secondaryAction.disabled}
                className="flex items-center gap-2"
              >
                {secondaryAction.icon && <secondaryAction.icon className="h-4 w-4" />}
                {secondaryAction.label}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Specialized table empty state components for common scenarios

export const NoDataFound: React.FC<{
  title?: string;
  description?: string;
  onRefresh?: () => void;
  onCreate?: () => void;
  isLoading?: boolean;
}> = ({
  title = 'No data found',
  description = 'There are no items to display at the moment.',
  onRefresh,
  onCreate,
  isLoading = false,
}) => (
  <TableEmptyState
    icon={Database}
    title={title}
    description={description}
    action={onCreate ? {
      label: 'Create New',
      onClick: onCreate,
      icon: Plus,
    } : undefined}
    secondaryAction={onRefresh ? {
      label: isLoading ? 'Refreshing...' : 'Refresh',
      onClick: onRefresh,
      variant: 'outline',
      icon: RefreshCw,
      disabled: isLoading,
    } : undefined}
  />
);

export const NoSearchResults: React.FC<{
  searchQuery: string;
  onClearSearch: () => void;
  onRefresh?: () => void;
  entityName?: string;
}> = ({
  searchQuery,
  onClearSearch,
  onRefresh,
  entityName = 'items',
}) => (
  <TableEmptyState
    icon={Search}
    title="No matching results"
    description={`No ${entityName} match "${searchQuery}". Try adjusting your search criteria.`}
    action={{
      label: 'Clear Search',
      onClick: onClearSearch,
      variant: 'outline',
    }}
    secondaryAction={onRefresh ? {
      label: 'Refresh',
      onClick: onRefresh,
      variant: 'ghost',
      icon: RefreshCw,
    } : undefined}
  />
);

export const NoOrganizationSelected: React.FC<{
  description?: string;
}> = ({
  description = 'Please select an organization to view data.',
}) => (
  <TableEmptyState
    icon={AlertCircle}
    title="No organization selected"
    description={description}
    size="sm"
  />
);

export const ConnectionRequired: React.FC<{
  onConnect?: () => void;
  serviceName?: string;
}> = ({
  onConnect,
  serviceName = 'service',
}) => (
  <TableEmptyState
    icon={AlertCircle}
    title={`${serviceName} connection required`}
    description={`Connect to ${serviceName} to view and manage your data.`}
    action={onConnect ? {
      label: `Connect to ${serviceName}`,
      onClick: onConnect,
    } : undefined}
  />
);

export default TableEmptyState;
