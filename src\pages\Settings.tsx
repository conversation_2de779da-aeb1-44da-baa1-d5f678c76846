// Settings Page - Sync configuration and preferences
// Manages sync settings, preferences, and configuration options

import React, { useCallback } from "react";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import Settings from "@/components/Settings";
import { useOrganizations } from "@/hooks/useOrganizations";
import { SyncLoadingOverlay } from "@/components/common/PageLoadingOverlay";
import { PageHeader } from "@/components/common/PageHeader";

/**
 * Settings Page Component
 *
 * Sync configuration interface providing:
 * - Sync preferences and settings
 * - Integration configuration options
 * - Data mapping and field settings
 * - Notification and alert preferences
 *
 * @returns {JSX.Element} The complete settings interface
 */
const SettingsPage: React.FC = () => {
  // Use custom hook for organizations management
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
    connectedOrganizations,
    refreshOrganizations,
    selectOrganization,
  } = useOrganizations({
    autoLoad: true,
    autoSelect: true,
    showErrorToasts: true,
    showSuccessToasts: true,
  });

  /**
   * Handle organization selection change
   */
  const handleOrganizationChange = useCallback(
    (organizationId: string) => {
      selectOrganization(organizationId);
    },
    [selectOrganization]
  );

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-amber-50 relative">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0 relative">
          {/* Header */}
          <PageHeader
            icon="⚙️"
            title="Sync Settings"
            description="Configure sync preferences and integration settings"
            organizations={organizations}
            selectedOrganization={selectedOrganization}
            connectedOrganizations={connectedOrganizations}
            isLoading={companiesLoading}
            showConnectionStatus={true}
            onOrganizationChange={handleOrganizationChange}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-8 bg-amber-50 relative">
            <div className="max-w-7xl mx-auto">
              <Settings />
            </div>

            {/* Loading Overlay */}
            <SyncLoadingOverlay
              isLoading={companiesLoading}
              operation="settings"
            />
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default React.memo(SettingsPage);
