// Sync Management Redux Actions
// Handles all sync-related async thunks for data synchronization

import { createAsyncThunk } from '@reduxjs/toolkit';
import {
  syncApi,
  SyncStatusResponse,
  SyncTriggerRequest,
  SyncTriggerResponse,
  SyncHistoryFilters,
  SyncHistoryResponse,
  SyncEntity
} from '../../api/sync.api';
import { syncTriggerRequestSchema } from '../../lib/validation';
import type { RootState, AppDispatch } from '../index';

/**
 * Get sync status for all entities
 * GET /api/v1/sync/status
 */
export const getSyncStatus = createAsyncThunk<
  SyncStatusResponse,
  string, // companyId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'sync/getStatus',
  async (companyId, { rejectWithValue }) => {
    try {
      const response = await syncApi.getStatus(companyId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get sync status');
    }
  }
);

/**
 * Trigger sync for specific entities
 * POST /api/v1/sync/trigger
 */
export const triggerSync = createAsyncThunk<
  SyncTriggerResponse,
  SyncTriggerRequest,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'sync/trigger',
  async (request, { rejectWithValue }) => {
    try {
      // Validate the request using Zod schema
      console.log('Request:', request);
      const validationResult = syncTriggerRequestSchema.safeParse(request);
      console.log('Validation result:', validationResult);
      if (!validationResult.success) {
        const errorMessages = validationResult.error.errors.map(err => err.message).join(', ');
        return rejectWithValue(`Validation failed: ${errorMessages}`);
      }

      const response = await syncApi.triggerSync(validationResult.data as SyncTriggerRequest);
      return response;
    } catch (error) {
      console.log('Error:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to trigger sync');
    }
  }
);

/**
 * Trigger sync for all entities
 * POST /api/v1/sync/trigger-all
 */
export const triggerAllSync = createAsyncThunk<
  SyncTriggerResponse,
  { companyId: string; force?: boolean },
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'sync/triggerAll',
  async ({ companyId, force }, { rejectWithValue }) => {
    try {
      const response = await syncApi.triggerAllSync(companyId, force);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to trigger all sync');
    }
  }
);

/**
 * Get sync history with filtering
 * GET /api/v1/sync/history
 */
export const getSyncHistory = createAsyncThunk<
  SyncHistoryResponse,
  SyncHistoryFilters,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'sync/getHistory',
  async (filters, { rejectWithValue }) => {
    try {
      const response = await syncApi.getHistory(filters);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get sync history');
    }
  }
);

/**
 * Get supported entities list
 * GET /api/v1/sync/entities
 */
export const getSyncEntities = createAsyncThunk<
  SyncEntity[],
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'sync/getEntities',
  async (_, { rejectWithValue }) => {
    try {
      const response = await syncApi.getEntities();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get sync entities');
    }
  }
);

// Export all sync actions
export const syncActions = {
  getSyncStatus,
  triggerSync,
  triggerAllSync,
  getSyncHistory,
  getSyncEntities,
};

export default syncActions;
