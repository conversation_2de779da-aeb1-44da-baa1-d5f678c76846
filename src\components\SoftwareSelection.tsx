import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface SoftwareSelectionProps {
  onSoftwareSelect: (software: string) => Promise<void>;
  isLoading?: boolean;
}

const SoftwareSelection: React.FC<SoftwareSelectionProps> = ({
  onSoftwareSelect,
  isLoading = false,
}) => {
  const handleXeroConnect = async () => {
    await onSoftwareSelect("xero");
  };
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">
          Select a software you wish to connect
        </h1>
        <p className="text-muted-foreground">
          You will be prompted to sign in to your accounts to authorize the
          secure sharing of your financial information
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <Card className="p-6 cursor-not-allowed opacity-50 transition-shadow relative">
          <div className="flex items-center justify-center h-20">
            <div className="text-green-600 font-bold text-xl">QuickBooks</div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center bg-black/10 rounded-lg">
            <div className="bg-white px-3 py-1 rounded-full text-sm font-medium text-gray-600 shadow-sm">
              Coming Soon
            </div>
          </div>
        </Card>

        <Card
          className={`p-6 cursor-pointer hover:shadow-md transition-shadow border-2 ${
            isLoading ? "opacity-50 cursor-not-allowed" : ""
          }`}
          style={{ borderColor: "#0078c8" }}
          onClick={isLoading ? undefined : handleXeroConnect}
        >
          <div className="flex items-center justify-center h-20">
            <div className="font-bold text-3xl" style={{ color: "#0078c8" }}>
              {isLoading ? "Connecting..." : "xero"}
            </div>
          </div>
        </Card>
      </div>

      <div className="flex justify-start mt-8">
        <Button
          onClick={handleXeroConnect}
          disabled={isLoading}
          className="text-white hover:opacity-90"
          style={{ backgroundColor: "#0078c8" }}
        >
          {isLoading ? "Connecting..." : "Connect"}
        </Button>
      </div>
    </div>
  );
};

export default SoftwareSelection;
