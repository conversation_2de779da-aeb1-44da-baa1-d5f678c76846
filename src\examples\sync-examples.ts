// Sync Examples
// Complete examples showing how to use the sync functionality

import {
  createSingleEntitySyncRequest,
  createMultiEntitySyncRequest,
  createFullSyncRequest,
  SYNC_ENTITIES,
  SYNC_PRIORITIES
} from '../utils/sync.utils';

/**
 * Example 1: Sync a single entity
 * This shows how to sync just one entity (e.g., 'Accounts') with normal priority
 */
export const syncSingleEntityExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Replace with actual company UUID

  // Basic single entity sync
  const basicSync = createSingleEntitySyncRequest(companyId, 'Accounts');
  // Output: { companyId: "...", entities: ["Accounts"], priority: "NORMAL", fullSync: false }

  // Single entity sync with high priority and full sync
  const prioritySync = createSingleEntitySyncRequest(
    companyId,
    'Bank Transactions',
    { priority: 'HIGH', fullSync: true }
  );
  // Output: { companyId: "...", entities: ["Bank Transactions"], priority: "HIGH", fullSync: true }

  // Reports entity sync (automatically maps to 3 entities)
  const reportsSync = createSingleEntitySyncRequest(companyId, 'Reports');
  // Output: { companyId: "...", entities: ["BalanceSheet", "ProfitLoss", "TrialBalance"], priority: "NORMAL", fullSync: false }

  return { basicSync, prioritySync, reportsSync };
};

/**
 * Example 2: Sync multiple specific entities
 * This shows how to sync several entities at once
 */
export const syncMultipleEntitiesExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Replace with actual company UUID

  // Sync financial entities
  const financialSync = createMultiEntitySyncRequest(
    companyId,
    ['Accounts', 'Bank Transactions', 'Invoices', 'Payments'],
    { priority: 'HIGH', fullSync: false }
  );

  // Sync contact and inventory entities with full sync
  const contactInventorySync = createMultiEntitySyncRequest(
    companyId,
    ['Contacts', 'Items', 'Purchase Orders'],
    { priority: 'NORMAL', fullSync: true }
  );

  return { financialSync, contactInventorySync };
};

/**
 * Example 3: Full sync of all entities
 * This shows how to sync all available entities
 */
export const syncAllEntitiesExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Replace with actual company UUID

  // Full sync of all entities
  const fullSyncAll = createFullSyncRequest(companyId, { priority: 'HIGH' });

  // Full sync of specific subset
  const fullSyncSubset = createFullSyncRequest(
    companyId,
    {
      priority: 'NORMAL',
      entities: ['Accounts', 'Contacts', 'Invoices', 'Payments', 'Items']
    }
  );

  return { fullSyncAll, fullSyncSubset };
};

/**
 * Example 4: Using the sync functionality in a React component
 * This shows how to integrate with Redux actions
 */
export const reactComponentExample = `
import React from 'react';
import { useAppDispatch } from '@/store/hooks';
import { triggerSync } from '@/store/actions/sync.actions';
import { createSingleEntitySyncRequest, createFullSyncRequest } from '@/utils/sync.utils';

const SyncComponent: React.FC = () => {
  const dispatch = useAppDispatch();
  const companyId = "your-company-uuid-here";

  const handleSyncSingleEntity = async () => {
    try {
      const syncRequest = createSingleEntitySyncRequest(
        companyId,
        'Accounts',
        { priority: 'HIGH', fullSync: false }
      );

      await dispatch(triggerSync(syncRequest)).unwrap();
      // Single entity sync completed successfully
    } catch (error) {
      // Handle single entity sync error
      console.error('Single entity sync failed:', error);
    }
  };

  const handleFullSync = async () => {
    try {
      const syncRequest = createFullSyncRequest(
        companyId,
        { priority: 'HIGH' }
      );

      await dispatch(triggerSync(syncRequest)).unwrap();
      // Full sync completed successfully
    } catch (error) {
      // Handle full sync error
      console.error('Full sync failed:', error);
    }
  };

  return (
    <div>
      <button onClick={handleSyncSingleEntity}>
        Sync Accounts
      </button>
      <button onClick={handleFullSync}>
        Full Sync All Entities
      </button>
    </div>
  );
};
`;

/**
 * Example 5: Manual sync request construction
 * This shows how to manually create sync requests following the Zod schema
 */
export const manualSyncRequestExamples = () => {
  const companyId = "123e4567-e89b-12d3-a456-************";

  // Manual single entity sync request
  const singleEntityRequest = {
    companyId: companyId,
    entities: ['Accounts'] as const,
    priority: 'NORMAL' as const,
    fullSync: false
  };

  // Manual multiple entities sync request
  const multipleEntitiesRequest = {
    companyId: companyId,
    entities: ['Accounts', 'Bank Transactions', 'Invoices'] as const,
    priority: 'HIGH' as const,
    fullSync: false
  };

  // Manual full sync request
  const fullSyncRequest = {
    companyId: companyId,
    entities: [...SYNC_ENTITIES], // All entities
    priority: 'HIGH' as const,
    fullSync: true
  };

  return {
    singleEntityRequest,
    multipleEntitiesRequest,
    fullSyncRequest
  };
};

/**
 * Example 6: Validation examples
 * This shows what happens with invalid requests
 */
export const validationExamples = () => {
  const companyId = "123e4567-e89b-12d3-a456-************";

  // Valid request
  const validRequest = {
    companyId: companyId,
    entities: ['Accounts', 'Contacts'],
    priority: 'NORMAL',
    fullSync: false
  };

  // Invalid requests (these would fail validation)
  const invalidRequests = {
    missingCompanyId: {
      // companyId: missing
      entities: ['Accounts'],
      priority: 'NORMAL',
      fullSync: false
    },

    emptyEntities: {
      companyId: companyId,
      entities: [], // Empty array not allowed
      priority: 'NORMAL',
      fullSync: false
    },

    invalidEntity: {
      companyId: companyId,
      entities: ['InvalidEntity'], // Not in SYNC_ENTITIES
      priority: 'NORMAL',
      fullSync: false
    },

    duplicateEntities: {
      companyId: companyId,
      entities: ['Accounts', 'Accounts'], // Duplicates not allowed
      priority: 'NORMAL',
      fullSync: false
    },

    invalidPriority: {
      companyId: companyId,
      entities: ['Accounts'],
      priority: 'INVALID', // Not in SYNC_PRIORITIES
      fullSync: false
    }
  };

  return { validRequest, invalidRequests };
};

/**
 * Available entities and priorities for reference
 */
export const availableOptions = {
  entities: SYNC_ENTITIES,
  priorities: SYNC_PRIORITIES,

  // Categorized entities for easier selection
  categorizedEntities: {
    financial: ['Accounts', 'Bank Transactions', 'Bank Transfers', 'Invoices', 'Payments', 'Receipts'],
    inventory: ['Items', 'Purchase Orders', 'Quotes'],
    contacts: ['Contacts'],
    reporting: ['Reports (P&L, Balance Sheet, Trial Balance)', 'Budgets'],
    configuration: ['Tax Rates', 'Tracking Categories'],
    documents: ['Attachments', 'Credit Notes', 'Repeating Invoices'],
    journals: ['Journals', 'Manual Journals']
  }
};

/**
 * Usage summary
 */
export const usageSummary = `
SYNC REQUEST STRUCTURE:
{
  companyId: string (UUID format required),
  entities: string[] (must be from SYNC_ENTITIES array, no duplicates, min 1 item),
  priority?: 'HIGH' | 'NORMAL' | 'LOW' (optional, defaults to 'NORMAL'),
  fullSync?: boolean (optional, defaults to false)
}

AVAILABLE ENTITIES:
${SYNC_ENTITIES.map(entity => `- ${entity}`).join('\n')}

AVAILABLE PRIORITIES:
${SYNC_PRIORITIES.map(priority => `- ${priority}`).join('\n')}

USAGE PATTERNS:
1. Single Entity: Use createSingleEntitySyncRequest()
2. Multiple Entities: Use createMultiEntitySyncRequest()
3. Full Sync: Use createFullSyncRequest()
4. Manual: Construct object following the schema above
`;

// Export all examples
export default {
  syncSingleEntityExample,
  syncMultipleEntitiesExample,
  syncAllEntitiesExample,
  reactComponentExample,
  manualSyncRequestExamples,
  validationExamples,
  availableOptions,
  usageSummary
};
