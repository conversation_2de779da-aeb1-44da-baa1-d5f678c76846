import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle,
  Building,
  Calendar,
  User,
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  initiateXeroOAuth,
  disconnectXero,
} from "@/store/actions/auth.actions";
import { toast } from "@/components/ui/sonner";
import SoftwareSelection from "./SoftwareSelection";

interface XeroConnectionProps {
  onConnect: () => void;
  onDisconnect: () => void;
}

const XeroConnection: React.FC<XeroConnectionProps> = ({
  onConnect,
  onDisconnect,
}) => {
  const dispatch = useAppDispatch();
  const { isConnected, companyInfo, isLoading } = useAppSelector(
    (state) => state.auth
  );
  const [currentStep, setCurrentStep] = useState<"selection" | "connected">(
    "selection"
  );

  const handleSoftwareSelect = async (software: string) => {
    if (software === "xero") {
      try {
        // Generate and store authentication key for normal authentication flow
        const authKey = `xero_auth_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;
        localStorage.setItem("xero_auth_key", authKey);

        // Get OAuth URL from backend
        const result: any = await dispatch(initiateXeroOAuth()).unwrap();

        if (result.data.url) {
          // Redirect to Xero OAuth URL
          window.location.href = result.data.url;
        } else {
          throw new Error("No OAuth URL received");
        }
      } catch (error) {
        console.error("Failed to initiate OAuth:", error);
        toast.error("Connection failed", {
          description: "Unable to connect to Xero. Please try again.",
          duration: 4000,
        });
      }
    }
  };

  const handleDisconnect = async () => {
    try {
      await dispatch(disconnectXero()).unwrap();
      setCurrentStep("selection");
      onDisconnect();
    } catch (error) {
      console.error("Disconnect failed:", error);
    }
  };

  if (currentStep === "selection" || !isConnected) {
    return (
      <SoftwareSelection
        onSoftwareSelect={handleSoftwareSelect}
        isLoading={isLoading}
      />
    );
  }

  // Connected state UI
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Xero Connection
            </CardTitle>
            <CardDescription>
              Manage your Xero API connection and view organization details
            </CardDescription>
          </div>
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Connected
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <Building className="h-4 w-4" />
                Organization Details
              </h4>
              <div className="bg-muted/50 p-3 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Company Name:
                  </span>
                  <span className="text-sm font-medium">
                    {companyInfo?.name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Short Code:
                  </span>
                  <span className="text-sm font-medium">
                    {companyInfo?.shortCode}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Base Currency:
                  </span>
                  <span className="text-sm font-medium">
                    {companyInfo?.baseCurrency}
                  </span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <User className="h-4 w-4" />
                Additional Info
              </h4>
              <div className="bg-muted/50 p-3 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Country:
                  </span>
                  <span className="text-sm font-medium">
                    {companyInfo?.countryCode}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Tax Number:
                  </span>
                  <span className="text-sm font-medium">
                    {companyInfo?.taxNumber}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Type:</span>
                  <span className="text-sm font-medium">
                    {companyInfo?.organisationType}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end pt-4">
            <Button
              variant="destructive"
              onClick={handleDisconnect}
              disabled={isLoading}
            >
              {isLoading ? "Disconnecting..." : "Disconnect from Xero"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default XeroConnection;
