import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SyncLogsState, SyncLog, SyncLogFilters } from '../types/syncLogs.types';
import { ApiError } from '../types';
import {
  fetchSyncLogs as fetchSyncLogsAction,
  retrySyncOperation,
  clearSyncLogs,
  getCompanySyncLogs,
  getDetailedSyncLog
} from '../actions/syncLogs.actions';

// Mock data matching the new API structure

// Initial state
const initialState: SyncLogsState = {
  logs: [],
  filteredLogs: [],
  filters: {
    startDate: '', // No default date
    endDate: '',   // No default date
    dateFrom: '',
    dateTo: '',
    endpoint: 'all',
    status: 'all',
    search: '',
    searchQuery: '',
  },
  selectedLog: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasNext: false,
    hasPrev: false,
  },
};

// Note: fetchSyncLogs is imported from actions/syncLogs.actions.ts

// Note: retrySyncOperation is imported from actions/syncLogs.actions.ts

// Note: Filtering is now handled by the API, not locally

// Slice
const syncLogsSlice = createSlice({
  name: 'syncLogs',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<SyncLogsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
      // Note: filteredLogs will be updated by API response, not local filtering
      // Reset pagination when filters change
      state.pagination.page = 1;
    },
    setSelectedLog: (state, action: PayloadAction<SyncLog | null>) => {
      state.selectedLog = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addSyncLog: (state, action: PayloadAction<SyncLog>) => {
      state.logs.unshift(action.payload); // Add to beginning
      state.filteredLogs.unshift(action.payload); // Add to filtered logs as well
    },
    clearLogs: (state) => {
      state.logs = [];
      state.filteredLogs = [];
      state.selectedLog = null;
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
        hasNext: false,
        hasPrev: false,
      };
    },
    setPagination: (state, action: PayloadAction<{ page?: number; limit?: number }>) => {
      if (action.payload.page !== undefined) {
        state.pagination.page = action.payload.page;
      }
      if (action.payload.limit !== undefined) {
        state.pagination.limit = action.payload.limit;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch sync logs (legacy action)
    builder
      .addCase(fetchSyncLogsAction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSyncLogsAction.fulfilled, (state, action: any) => {
        console.log('fetchSyncLogsAction.fulfilled', action.payload);
        state.isLoading = false;
        // Handle API response with pagination
        state.logs = action.payload.data || []
        state.filteredLogs = action.payload.data || [];
        state.pagination = action.payload.pagination || state.pagination;
      })
      .addCase(fetchSyncLogsAction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to fetch sync logs';
      });

    // Get company sync logs
    builder
      .addCase(getCompanySyncLogs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCompanySyncLogs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.logs = action.payload.items;
        state.filteredLogs = action.payload.items;
        state.pagination = action.payload.pagination;
      })
      .addCase(getCompanySyncLogs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to get company sync logs';
      });

    // Get detailed sync log
    builder
      .addCase(getDetailedSyncLog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getDetailedSyncLog.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedLog = action.payload;
      })
      .addCase(getDetailedSyncLog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to get detailed sync log';
      });

    // Retry sync operation
    builder
      .addCase(retrySyncOperation.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(retrySyncOperation.fulfilled, (state) => {
        state.isLoading = false;
        // The retry operation returns success info, not a new log
        // We should refresh the logs instead
      })
      .addCase(retrySyncOperation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || 'Failed to retry sync operation';
      });
  },
});

export const {
  setFilters,
  setSelectedLog,
  clearError,
  addSyncLog,
  clearLogs,
  setPagination
} = syncLogsSlice.actions;

export default syncLogsSlice.reducer;
